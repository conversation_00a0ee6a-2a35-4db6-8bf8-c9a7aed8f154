<script lang="ts" setup>
import {
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  InputNumber as hInputNumber,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Checkbox as hCheckbox,
  Select as hSelect,
  Textarea as hTextarea,
  Button as hButton,
  message,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from 'vue';
import type { Ref } from 'vue';
import type { IDomEditor } from '@wangeditor/editor';
import Editor from '@haierbusiness-front/components/editor/Editor.vue';
import { IAnnouncementNotice } from '@haierbusiness-front/common-libs';
import { announcementNoticeApi } from '@haierbusiness-front/apis';
import {
  announcementNoticeStateOptions,
  announcementContentFormOptions,
  announcementEffectScopeOptions,
  AnnouncementContentForm,
} from '@haierbusiness-front/common-libs';
import type { Rule } from 'ant-design-vue/lib/form';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();

interface Props {
  show: boolean;
  data: IAnnouncementNotice | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

// 使用IAnnouncementNotice接口定义默认数据
const defaultData: IAnnouncementNotice = {
  id: null,
  name: '',
  code: '',
  state: '正常', // 默认正常状态
  description: '',
  title: '',
  effectScope: undefined,
  contentForm: AnnouncementContentForm.TEXT, // 默认选择文本
  informContent: '',
  sort: 1,
  isWindow: false,
};

// 选项数据，使用已有的枚举常量
const effectScopeOptions = announcementEffectScopeOptions;
const stateOptions = announcementNoticeStateOptions;
const contentFormOptions = announcementContentFormOptions;

// URL验证正则表达式
const urlPattern = /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/;

// 表单验证规则，支持动态验证
const rules = computed(() => {
  const baseRules = {
    title: [{ required: true, message: '标签名称必填' }],
    effectScope: [{ required: true, message: '通知作用范围必填' }],
    contentForm: [{ required: true, message: '内容形式必填' }],
    sort: [{ required: true, message: '排序必填' }, { type: 'number', min: 1, message: '排序必须大于0' } as Rule],
    state: [{ required: true, message: '状态必填' }],
    isWindow: [{ required: true, message: '是否弹框通知必填' }],
  };

  // 根据内容形式动态设置公告内容的验证规则
  let informContentRules = [{ required: true, message: '公告内容必填' }];
  
  // 如果选择链接模式(contentForm=2)，添加URL格式验证
  if (announcementNotice.value.contentForm === 2) {
    informContentRules = [{
      pattern: urlPattern,
      message: '请输入正确的链接地址格式'
    }];
  }

  return {
    ...baseRules,
    informContent: informContentRules,
  };
});

const announcementNotice: Ref<IAnnouncementNotice> = ref(props.data ? { ...props.data } : { ...defaultData });

// 组件挂载时检查初始数据
onMounted(() => {
  getDetail()

});

// 获取详情数据
const getDetail = async () => {
  const id = Number(route.query.id);
  if (!id) return;

  confirmLoading.value = true;
  try {
    const res = await announcementNoticeApi.details(id);
    announcementNotice.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    confirmLoading.value = false;
  }
};

const emit = defineEmits(['cancel', 'ok']);

const visible = computed(() => props.show);

// 处理select值类型转换的函数
const handleNumberChange = (value: any, field: keyof IAnnouncementNotice) => {
  if (value !== undefined && value !== null) {
    // 对于state字段，它是字符串类型，不需要进行parseInt转换
    if (field === 'state') {
      announcementNotice.value[field] = value;
    } else {
      announcementNotice.value[field] = typeof value === 'string' ? parseInt(value) : value;
    }

    // 当内容形式发生切换时，清空之前的内容
    if (field === 'contentForm') {
      announcementNotice.value.informContent = '';
    }
  } else {
    announcementNotice.value[field] = undefined;
  }
};

// 富文本编辑器内容变化处理函数
const onEditorChange = (editor: IDomEditor) => {
  announcementNotice.value.informContent = editor.getHtml();
};

// 处理InputNumber值的转换函数
const handleInputNumberChange = (value: any) => {
  announcementNotice.value.sort = typeof value === 'number' ? value : undefined;
};

const handleOk = async () => {
  confirmLoading.value = true;
  try {
    await from.value.validate();

    // 确保获取到所有表单值
    const formValues = {
      id: Number(route.query.id),
      title: announcementNotice.value.title,
      effectScope: announcementNotice.value.effectScope,
      contentForm: announcementNotice.value.contentForm,
      informContent: announcementNotice.value.informContent,
      sort: announcementNotice.value.sort,
      state: announcementNotice.value.state,
      isWindow: announcementNotice.value.isWindow,
    };
    console.log('表单数据:', formValues);
    // 根据编辑模式调用不同API
    const apiMethod = route.query.id ? announcementNoticeApi.edit : announcementNoticeApi.save;
      apiMethod(formValues as any)
        .then(() => {
          // 成功处理
          confirmLoading.value = false;
          message.success(route.query.id ? '编辑成功' : '添加成功');
          router.push('/bidman/announcementNotice/index');
        })
        .catch((error) => {
          // 错误处理
          message.success(route.query.id ? '编辑失败' : '添加失败');
          console.error('API请求失败:', error);
          confirmLoading.value = false;
        });
  } catch (error: any) {
    console.error('操作失败:', error);
    confirmLoading.value = false;
  }
};

const handlecontentForm = (value: number)=>{
  if(value == 2){
    announcementNotice.value.isWindow = false
  }
}
</script>

<template>
  <div class="edit-modal-content">
    <h-form ref="from" :model="announcementNotice" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }"  :rules="rules">
      <h-form-item label="标题" name="title">
        <h-input
          :value="announcementNotice.title"
          @update:value="announcementNotice.title = $event"
          placeholder="请输入标题"
          :maxlength="200"
          show-count
        />
      </h-form-item>
      <h-form-item label="通知作用范围" name="effectScope">
        <h-select
          :value="announcementNotice.effectScope"
          @update:value="handleNumberChange($event, 'effectScope')"
          placeholder="请选择通知作用范围"
          :options="effectScopeOptions"
          allow-clear
          mode="multiple"
        />
      </h-form-item>
      <h-form-item label="内容形式" name="contentForm">
        <h-radio-group
          :value="announcementNotice.contentForm"
          @change="handlecontentForm"
          @update:value="handleNumberChange($event, 'contentForm')"
        >
          <h-radio v-for="option in contentFormOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </h-radio>
        </h-radio-group>
      </h-form-item>
      <h-form-item label="公告内容" name="informContent">
        <!-- 内容形式为文本(1)时显示富文本框 -->
        <template v-if="announcementNotice.contentForm === 1">
          <Editor
            height="350px"
            :modelValue="announcementNotice.informContent || ''"
            @change="onEditorChange"
            uploadUrl="/upload"
          />
        </template>
        <!-- 内容形式为链接(2)时显示普通文本框 -->
        <h-input
          v-else
          :value="announcementNotice.informContent"
          @update:value="announcementNotice.informContent = $event"
          placeholder="请输入链接地址"
        />
      </h-form-item>
      <h-form-item label="排序" name="sort">
        <h-input-number
          :value="announcementNotice.sort"
          @update:value="handleInputNumberChange($event)"
          placeholder="请输入排序"
          :min="1"
        />
      </h-form-item>
      <h-form-item label="状态" name="state">
        <h-select
          :value="announcementNotice.state"
          @update:value="handleNumberChange($event, 'state')"
          placeholder="请选择状态"
          :options="stateOptions"
        />
      </h-form-item>
      <h-form-item label="是否弹窗" name="isWindow">
        <h-radio-group :value="announcementNotice.isWindow" @update:value="announcementNotice.isWindow = $event" :disabled="announcementNotice.contentForm == 2">
          <h-radio :value="true">是</h-radio>
          <h-radio :value="false">否</h-radio>
        </h-radio-group>
      </h-form-item>
      <h-form-item style="text-align: center;">
        <h-button type="primary" :loading="confirmLoading" @click="handleOk">{{announcementNotice.id ? '确认编辑' : '确认新增'}}</h-button>
      </h-form-item>
    </h-form>
  </div>
</template>

<style lang="less" scoped>
.edit-modal-content {
  padding: 24px;
  background: #fff;
  min-height: 100vh;
}
.edit-modal {
  &-content {
    padding: 10px;
    .group-title {
      color: rgba(0, 0, 0, 0.88);
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 15px;
    }
  }
}
.ant-form{
  width: 85%;
}
.button-container {
  margin-top: 24px;
  padding: 16px 0;
}

.button-container .ant-btn:hover {
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
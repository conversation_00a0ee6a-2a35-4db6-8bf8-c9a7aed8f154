<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  InputNumber as hInputNumber,
  Upload as hUpload,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  message,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { tourismProductsApi } from '@haierbusiness-front/apis';
import {
  ITourismProductsFilter,
  ITourismProducts
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    listApiRun({
      pageNum:1,
      pageSize:10,
    })
})

const columns: ColumnType[] = [
  {
    title: '标题',
    dataIndex: 'title',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品简介',
    dataIndex: 'briefIntroduction',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '联系人',
    dataIndex: 'nickname',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record,text }) => `${record.nickname}/${record.username}` || text
  },
  {
    title: '出发城市',
    dataIndex: 'startCity',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '市场价格',
    dataIndex: 'marketPrice',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text != null ? `${text}元` : '',
  },
  {
    title: '员工价格',
    dataIndex: 'staffPrice',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => text != null ? `${text}元` : '',
  },
  {
    title: '生效时间',
    dataIndex: 'startTime',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改人',
    dataIndex: 'lastModifiedName',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '修改时间',
    dataIndex: 'gmtModified',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center'
  },
];
const searchParam = ref<ITourismProductsFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(tourismProductsApi.list);

const reset = () => {
  searchParam.value = {}
  gmtCreate.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const edit = (id?: number) => {
  currentRouter.value.push({ path: "/bidman/tourismProducts/edit", query: { id: id } })
}
const add = (id?: number) => {
  currentRouter.value.push({ path: "/bidman/tourismProducts/edit"})
}


// 删除
const { handleDelete } = useDelete(tourismProductsApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const gmtCreate = ref<[Dayjs, Dayjs]>()
watch(() => gmtCreate.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.begin = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.end = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="1" class="label-right modify">
            <label for="merchantName">标题：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantName" v-model:value="searchParam.title" placeholder="请输入" allow-clear
              :maxlength="200" />
          </h-col>
          <h-col :span="2" class="label-right modify">
            <label for="merchantName">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantName" v-model:value="searchParam.createName" placeholder="请输入" allow-clear
              :maxlength="200" />
          </h-col>
          <h-col :span="2" class="label-right modify">
            <label for="merchantName">出发城市：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="merchantName" v-model:value="searchParam.startCity" placeholder="请输入" allow-clear
              :maxlength="200" />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="gmtCreate" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="12" style="text-align: left;">
            <h-button type="primary" @click="add()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ x: 1200 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="edit(record.id)">编辑</h-button>
              
              <h-button type="link" @click="handleDelete(record.id)">删除</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.label-right {
  text-align: right;
}
</style>

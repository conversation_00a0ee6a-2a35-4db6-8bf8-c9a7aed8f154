<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { Key } from 'ant-design-vue/lib/vc-table/interface';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { directVisaHotelApi } from '@haierbusiness-front/apis';
import {
  IDirectVisaHotelFilter,
  IDirectVisaHotel
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted } from 'vue';
import { DataType, usePagination, useRequest } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
// const router = useRouter()

const currentRouter = ref()

onMounted(async () => {
    currentRouter.value = await router
    listApiRun({
      introduceMiceFlag:1,
      pageNum:1,
      pageSize:10,
    })
})

const columns: ColumnType[] = [
  {
    title: '酒店编号',
    dataIndex: 'code',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '酒店名称',
    dataIndex: 'name',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '酒店地址',
    dataIndex: 'address',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '酒店星级',
    dataIndex: 'starLevel',
    width: '150px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => hotalStarList.find((item)=> item.value == text)?.text || '暂无'
  },
  {
    title: '酒店区域',
    dataIndex: 'cityName',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record,text }) => `${record.cityName}/${record.regionName}` || text
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center'
  },
];

const hotalStarList = [
  { text: '白金五星级', value: 55 },
  { text: '五星级', value: 50 },
  { text: '准五星', value: 45 },
  { text: '四星级', value: 40 },
  { text: '准四星', value: 35 },

  { text: '三星级', value: 30 },
  { text: '准三星', value: 25 },
  { text: '二星级', value: 20 },
  { text: '准二星', value: 15 },

  { text: '一星级', value: 10 },
  { text: '准一星', value: 5 },
];


const searchParam = ref<IDirectVisaHotelFilter>({})
const {
  data,
  run: listApiRun,
  loading,
  current,
  pageSize,
} = usePagination(directVisaHotelApi.list);

const reset = () => {
  searchParam.value = {}
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
  filters?: any,
  sorter?: any,
) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  });
};
const { visible, editData, handleCreate, handleEdit, onDialogClose } =
  useEditDialog<IDirectVisaHotel, IDirectVisaHotel>(directVisaHotelApi, "直签酒店", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

const handleOk = ()=>{
  visible.value = false
}

const thisHandleEdit = (item: IDirectVisaHotel) => {
  const currentData = {
    ...item
  };
  editData.value = currentData
  visible.value = true
}


// 删除
const { handleDelete } = useDelete(directVisaHotelApi, () => listApiRun({
  ...searchParam.value,
  pageNum: data.value?.pageNum,
  pageSize: data.value?.pageSize,
}))

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(() => beginAndEnd.value, (n: any, o: any) => {
  if (n) {
    searchParam.value.begin = n[0]
    searchParam.value.end = n[1]
  } else {
    searchParam.value.begin = undefined
    searchParam.value.end = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createTime">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
                <h-button type="link"  @click="thisHandleEdit(record)">查看</h-button>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    
    <div v-if="visible">
        <edit-dialog
            :show="visible"
            :data="editData"
            @cancel="onDialogClose"
            @ok="handleOk"
        >
        </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">

.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

</style>

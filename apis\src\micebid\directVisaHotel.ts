import { download, get, post } from '../request'
import { 
    IDirectVisaHotelFilter, 
    IDirectVisaHotel,
    IPageResponse, 
    Result } from '@haierbusiness-front/common-libs'


export const directVisaHotelApi = {
    list: (params: IDirectVisaHotelFilter): Promise<IPageResponse<IDirectVisaHotel>> => {
        return get('/hotel/api/hotel/searchList', params)
    },

    get: (id: number): Promise<IDirectVisaHotel> => {
        return get('/mice-bid/api/directVisaHotel/get', {
            id
        })
    },

    save: (params: IDirectVisaHotel): Promise<Result> => {
        return post('/mice-bid/api/directVisaHotel/save', params)
    },

    edit: (params: IDirectVisaHotel): Promise<Result> => {
        return post('/mice-bid/api/directVisaHotel/update', params)
    },

    remove: (id: number): Promise<Result> => {
        return post('/mice-bid/api/directVisaHotel/delete', { id })
    },
}

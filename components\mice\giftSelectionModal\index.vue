<script setup lang="ts">
import {
  Modal as hModal,
  Input as hInput,
  But<PERSON> as hButton,
  Row as hRow,
  Col as hCol,
  Card as hCard,
  Image as hImage,
  Spin as hSpin,
  Pagination as hPagination,
  Empty as hEmpty,
  message,
} from 'ant-design-vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { ref, reactive, watch, onMounted, onUnmounted, defineProps, defineEmits, computed, nextTick, watchEffect } from 'vue';
import { pascalCaseApi } from '@haierbusiness-front/apis';
import { IPascalCase, IPascalCaseFilter } from '@haierbusiness-front/common-libs';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  currentGiftIndex: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['update:visible', 'selectGift']);

// 搜索参数
const searchParams = reactive<IPascalCaseFilter>({
  pageNum: 1,
  pageSize: 8,
  presentName: '',
});

// 数据状态
const loading = ref(false);
const giftList = ref<IPascalCase[]>([]);
const total = ref(0);

// 搜索关键词
const searchKeyword = ref('');

// 搜索防抖定时器
let searchTimer: NodeJS.Timeout | null = null;

// 详情弹框状态
const detailVisible = ref(false);
const selectedGift = ref<IPascalCase | null>(null);
const giftDetail = ref<any>(null);
const detailLoading = ref(false);

// 规格选择状态
const selectedSpecs = ref<Record<string, string>>({});
const selectedSku = ref<any>(null);

// 图片显示状态
const currentImageIndex = ref(0);

// 计算属性 - 当前图片列表
const currentImages = computed(() => {
  let images: string[] = [];

  if (selectedSku.value && selectedSku.value.path && selectedSku.value.path.length > 0) {
    images = selectedSku.value.path.map((item: any) => item.path);
  } else if (selectedGift.value) {
    // 如果没有SKU图片，使用默认图片
    const defaultImage = getImageUrl(selectedGift.value);
    if (defaultImage) {
      images = [defaultImage];
    }
  }

  return images;
});

// 计算属性 - 缩略图列表（始终显示5个）
const thumbnailImages = computed(() => {
  const images = currentImages.value;

  if (images.length === 0) {
    return [];
  }

  // 如果图片少于5张，重复填充到5张
  if (images.length < 5) {
    const result = [...images];
    while (result.length < 5) {
      // 循环添加已有图片直到达到5张
      const index = result.length % images.length;
      result.push(images[index]);
    }
    return result;
  }

  // 如果图片超过5张，只取前5张
  return images.slice(0, 5);
});

// 计算属性 - 当前主图
const currentMainImage = computed(() => {
  const images = currentImages.value;
  if (images.length > 0 && currentImageIndex.value < images.length) {
    return images[currentImageIndex.value];
  }
  return selectedGift.value ? getImageUrl(selectedGift.value) : '';
});

// 选择图片
const selectImage = (index: number) => {
  // 对于重复的图片，映射到实际的图片索引
  const actualIndex = index % currentImages.value.length;
  currentImageIndex.value = actualIndex;
};

// 当前选中的缩略图索引（用于显示选中状态）
const currentThumbnailIndex = ref(0);

// 选择缩略图
const selectThumbnail = (index: number) => {
  currentThumbnailIndex.value = index;
  // 映射到实际图片索引
  const actualIndex = index % currentImages.value.length;
  currentImageIndex.value = actualIndex;
};

// 监听礼品变化，重置选中状态
watch([selectedGift, selectedSku], () => {
  currentImageIndex.value = 0;
  currentThumbnailIndex.value = 0;
});

// 获取礼品列表
const fetchGiftList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchParams,
      presentName: searchKeyword.value,
    };
    const response = await pascalCaseApi.listPushDemand(params);
    giftList.value = response.records || [];
    total.value = response.total || 0;
  } catch (error) {
    message.error('获取礼品列表失败');
    console.error('获取礼品列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  searchParams.pageNum = 1;
  fetchGiftList();
};

// 分页变化
const handlePageChange = (page: number, pageSize: number) => {
  searchParams.pageNum = page;
  searchParams.pageSize = pageSize;
  fetchGiftList();
};

// 页大小变化
const handlePageSizeChange = (current: number, size: number) => {
  searchParams.pageNum = 1; // 切换每页条数时重置为第一页
  searchParams.pageSize = size;
  fetchGiftList();
};

// 更新选中的SKU
const updateSelectedSku = () => {
  if (!giftDetail.value || !giftDetail.value.priceConfig) {
    selectedSku.value = null;
    return;
  }

  // 构建当前选择的规格组合字符串
  const selectedSpecValues = Object.values(selectedSpecs.value);
  const specCombination = selectedSpecValues.join('/');

  // 在priceConfig中查找匹配的SKU
  const matchedSku = giftDetail.value.priceConfig.find((sku: any) =>
    sku.valueName === specCombination
  );

  selectedSku.value = matchedSku || null;

  // 重置图片索引到第一张
  currentImageIndex.value = 0;
};

// 获取商品详情
const fetchGiftDetail = async (giftId: number) => {
  try {
    detailLoading.value = true;
    const response = await pascalCaseApi.get(giftId);
    giftDetail.value = response;

    // 初始化规格选择状态
    selectedSpecs.value = {};
    selectedSku.value = null;

    // 如果有规格配置，设置默认选择第一个可用的规格
    if (response.config && response.config.length > 0) {
      // 为每个规格类型设置默认值
      response.config.forEach((spec: any) => {
        if (spec.listValueName && spec.listValueName.length > 0) {
          selectedSpecs.value[spec.name] = spec.listValueName[0].name;
        }
      });

      // 查找对应的SKU
      updateSelectedSku();
    }
  } catch (error) {
    message.error('获取商品详情失败');
    console.error('获取商品详情失败:', error);
  } finally {
    detailLoading.value = false;
  }
};

// 显示商品详情
const showGiftDetail = async (gift: IPascalCase) => {
  selectedGift.value = gift;
  detailVisible.value = true;

  // 获取详细信息
  if (gift.id) {
    await fetchGiftDetail(gift.id);
  }
};

// 选择规格
const selectSpec = (keyName: string, valueName: string) => {
  selectedSpecs.value[keyName] = valueName;
  updateSelectedSku();
};

// 检查规格组合是否可用
const isSpecCombinationAvailable = (keyName: string, valueName: string) => {
  if (!giftDetail.value || !giftDetail.value.priceConfig) {
    return true;
  }

  // 创建临时的规格选择状态
  const tempSpecs = { ...selectedSpecs.value, [keyName]: valueName };
  const tempSpecValues = Object.values(tempSpecs);
  const tempSpecCombination = tempSpecValues.join('/');

  // 查找匹配的SKU
  const matchedSku = giftDetail.value.priceConfig.find((sku: any) =>
    sku.valueName === tempSpecCombination
  );

  return matchedSku && matchedSku.state === true;
};

// 确认选择礼品
const confirmSelectGift = () => {
  if (selectedGift.value) {
    // 构建规格组合描述
    let specComb = '';
    if (selectedSpecs.value && Object.keys(selectedSpecs.value).length > 0) {
      specComb = Object.entries(selectedSpecs.value)
        .map(([key, value]) => `${key}：${value}`)
        .join('，');
    }

    // 获取单价 - 优先使用选中SKU的价格，否则使用礼品的销售价格
    const unitPrice = selectedSku.value?.salePrice || selectedGift.value.salePrice || 0;

    // 如果有选中的SKU，将SKU信息一起传递
    const giftData = {
      ...selectedGift.value,
      selectedSku: selectedSku.value,
      selectedSpecs: selectedSpecs.value,
      giftDetail: giftDetail.value,
      specComb: specComb || selectedGift.value.presentName,
      // 确保传递正确的价格
      salePrice: unitPrice,
      // 添加unitPrice字段传递给父组件
      unitPrice: unitPrice
    };

    emit('selectGift', giftData, props.currentGiftIndex);
    detailVisible.value = false;
    handleClose();
  }
};

// 关闭详情弹框
const closeDetail = () => {
  detailVisible.value = false;
  selectedGift.value = null;
  giftDetail.value = null;
  selectedSpecs.value = {};
  selectedSku.value = null;
  currentImageIndex.value = 0;
};

// 关闭弹框
const handleClose = () => {
  emit('update:visible', false);
};

// 监听弹框显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      fetchGiftList();
    }
  },
);

// 监听搜索关键词变化，实现自动搜索
watch(
  () => searchKeyword.value,
  () => {
    // 清除之前的定时器
    if (searchTimer) {
      clearTimeout(searchTimer);
    }

    // 设置防抖延迟（500ms）
    searchTimer = setTimeout(() => {
      searchParams.pageNum = 1;
      fetchGiftList();
    }, 500);
  },
);

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
});

// 获取图片URL
const getImageUrl = (gift: IPascalCase) => {
  if (gift.path && gift.path.length > 0) {
    return gift.path[0].path;
  }
  if (gift.images && gift.images.length > 0) {
    return gift.images[0].path;
  }
  return '';
};



// 格式化价格
const formatPrice = (price: number | undefined) => {
  if (price === undefined || price === null) return '暂无';
  return `¥${price.toFixed(2)}`;
};

const itemRefs: any = ref([])

const setItemRef = (el: HTMLElement | null) => {
  if (el) {
    itemRefs.value.push(el)
    console.log('当前元素宽度:', el.clientWidth) // 实时打印每个元素的宽度
  }
}

const maxWidth = computed(() => {
  return itemRefs.value.reduce((max, el) =>
    Math.max(max, el?.clientWidth || 0), 0
  )
})

watchEffect(() => {
  if (maxWidth.value && maxWidth.value != 0) {
    const width = maxWidth.value
    // console.log(width, "width");
    itemRefs.value.forEach(el => el && (el.style.width = `${width}px`))
  }
})

onMounted(() => {
  console.log();

})




</script>

<template>
  <h-modal v-model:open="props.visible" title="" width="800px" :footer="null" @cancel="handleClose" class="gift-selection-modal">
    <!-- 搜索区域 -->
    <div class="search-area">
      <h-input v-model:value="searchKeyword" placeholder="请输入关键词搜索" style="width: 300px; height: 32px;border: 0;" size="small" @pressEnter="handleSearch"
        :allowClear="true">
        <template #suffix>
          <h-button type="text" @click="handleSearch">
            <SearchOutlined />
          </h-button>
        </template>
      </h-input>
    </div>

    <!-- 礼品列表 -->
    <h-spin :spinning="loading">
      <div class="gift-list gift-selection-list" v-if="giftList.length > 0">
        <h-row :gutter="[8, 16]">
          <h-col :span="6" class="gift-col" v-for="gift in giftList" :key="gift.id || gift.presentName">
            <h-card hoverable :bordered="false" class="gift-card" @click="showGiftDetail(gift)">
              <template #cover>
                <div class="gift-image-container">
                  <h-image :src="getImageUrl(gift)" :preview="false"
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                    class="gift-image" />
                </div>
              </template>
              <div class="gift-content">
                <div class="gift-name" :title="gift.presentName">
                  {{ gift.presentName }}
                </div>

                <div class="gift-price">
                  <span class="sale-price">{{ formatPrice(gift.salePrice) }}</span>
                </div>
              </div>
            </h-card>
          </h-col>
        </h-row>
      </div>

      <!-- 空状态 -->
      <h-empty v-else description="暂无礼品数据" />
    </h-spin>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <h-pagination v-model:current="searchParams.pageNum" v-model:page-size="searchParams.pageSize" :total="total" :size="'small'"
        :pageSizeOptions="['8', '10', '20']" :defaultPageSize="8" :show-size-changer="true" :show-quick-jumper="true"
        @change="handlePageChange" @showSizeChange="handlePageSizeChange" />
    </div>
  </h-modal>

  <!-- 商品详情弹框 -->
  <h-modal v-model:open="detailVisible" title="商品详情" width="900px" @cancel="closeDetail" class="gift-detail-modal">
    <div class="detail-modal-content">
      <h-spin :spinning="detailLoading">
        <div v-if="selectedGift && giftDetail" class="gift-detail">
        <!-- 商品图片 -->
        <div class="detail-images">
          <!-- 主图显示 -->
          <div class="main-image-container">
            <h-image :src="currentMainImage" :preview="false"
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              class="detail-main-image" />
          </div>

          <!-- 缩略图列表 -->
          <div class="thumbnail-list" v-if="currentImages.length > 0">
            <div v-for="(image, index) in thumbnailImages" :key="index" class="thumbnail-item" :class="{
              'thumbnail-active': currentThumbnailIndex === index
            }" @click="selectThumbnail(index)">
              <h-image :src="image" :preview="false"
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                class="thumbnail-image" />
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="detail-info">
            <h3 class="detail-name">{{ giftDetail.presentName }}</h3>
            <div class="detail-desc" v-if="giftDetail.presentDesc">{{ giftDetail.presentDesc }}</div>

          <!-- 商品信息卡片 -->
          <div class="product-info-card">
            <div class="info-item">
              <div class="info-label">价格</div>
              <div class="info-value price-value">
                {{ selectedSku ? formatPrice(selectedSku.salePrice) : '请选择规格' }}
              </div>
            </div>
            <div class="info-item" v-if="giftDetail.spuId">
              <div class="info-label">编号</div>
              <div class="info-value">{{ giftDetail.spuId }}</div>
            </div>
          </div>

          <!-- 市场价格 -->
          <!-- <div class="market-price-row" v-if="selectedSku && selectedSku.marketPrice">
            <span class="market-price-label">市场价格：</span>
            <span class="market-price-value">{{ formatPrice(selectedSku.marketPrice) }}</span>
          </div> -->

          <!-- 规格选择 -->
          <div v-if="giftDetail.config && giftDetail.config.length > 0" class="spec-selection">
            <div v-for="spec in giftDetail.config" :key="spec.name" class="spec-group">
              <div :ref="setItemRef" class="spec-label">{{ spec.name }}：</div>
              <div class="spec-options">
                <div v-for="option in spec.listValueName" :key="option.id" class="spec-option" :class="{
                  'spec-option-selected': selectedSpecs[spec.name] === option.name,
                  'spec-option-disabled': !isSpecCombinationAvailable(spec.name, option.name)
                }" @click="isSpecCombinationAvailable(spec.name, option.name) && selectSpec(spec.name, option.name)">
                  {{ option.name }}
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </h-spin>
    </div>

    <template #footer>
      <div class="detail-footer">
        <h-button @click="closeDetail">取消</h-button>
        <h-button type="primary" @click="confirmSelectGift"
          :disabled="giftDetail && giftDetail.config && giftDetail.config.length > 0 && (!selectedSku || !selectedSku.state)">
          确认选择
        </h-button>
      </div>
    </template>
  </h-modal>
</template>

<style scoped lang="less">
.search-area {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 16px;

  :deep(.ant-input-affix-wrapper-sm) {
    padding: 0 0 0 7px;
  }
}

.gift-list {
  min-height: 490px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 10px;

  /* 隐藏WebKit浏览器的滚动条 */
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

.gift-col {
  display: flex;
  justify-content: center;
}

.gift-selection-list .gift-card {
  width: 170px;
  cursor: pointer;
  transition: box-shadow .2s cubic-bezier(.38,0,.24,1);
  border-radius: 0px;
  overflow: hidden;
  box-shadow: none;

  &:hover {
    // transform: translateY(-2px);
    box-shadow: 0px 1px 10px rgba(0, 0, 0, .05), 0px 4px 5px rgba(0, 0, 0, .08), 0px 2px 4px -1px rgba(0, 0, 0, .12);
  }

  // 只针对礼品选择列表内的卡片body
  :deep(.ant-card-body) {
    padding: 8px 12px !important;
  }
}

.gift-image-container {
  width: 170px;
  height: 170px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 0;
}

:deep(.gift-image) {
  width: 100%;
  height: 170px !important;
  object-fit: cover;
}

:deep(.ant-image){
  width: 100%;
  height: 100%;
}

.gift-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.gift-spec {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gift-price {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.sale-price {
  font-size: 16px;
  font-weight: 600;
  color: #f53f3f;
}

.market-price {
  font-size: 12px;
  color: #86909c;
  text-decoration: line-through;
}

.pagination-container {
  text-align: center;
}

.ml8 {
  margin-left: 8px;
  height: 40px;
}

/* 详情弹框样式 */
.detail-modal-content {
  // height: 490px; /* 与第一个弹框内容区域高度保持一致：搜索区域68px + 礼品列表350px + 分页区域72px */ /* 设置固定高度，与第一个弹框内容区域高度保持一致 */
  overflow-y: auto;
  /* 隐藏滚动条但保留滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  /* 隐藏WebKit浏览器的滚动条 */
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
}

.gift-detail {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.detail-images {
  flex: 0 0 332px;
  height: 400px;
  /* 减少总高度 */
  display: flex;
  flex-direction: column;
}

.main-image-container {
  flex: 0 0 300px;
  /* 固定主图高度为300px */
  margin-bottom: 8px;
  /* 增加与缩略图的间距 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.detail-main-image {
  max-width: 100%;
  max-height: 100%;
  /* 确保图片不超过容器高度 */
  width: auto;
  height: auto;
  object-fit: contain;
  /* 改为contain，确保完整显示图片 */
  border-radius: 8px;
}

.thumbnail-list {
  height: 70px;
  /* 固定高度 */
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  /* 不换行 */
  justify-content: flex-start;
  /* 从左侧开始排列 */
  overflow: hidden;
  /* 隐藏溢出部分 */
}

.thumbnail-item {
  flex: 0 0 60px;
  /* 固定宽度 */
  height: 60px;
  border: 2px solid transparent;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;

  &:hover:not(.thumbnail-single) {
    border-color: #1890ff;
  }
}

.thumbnail-active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.thumbnail-single {
  cursor: default;

  &:hover {
    border-color: transparent;
  }
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.detail-info {
  flex: 1;
}

.detail-name {
  font-size: 20px;
  font-weight: 600;
  color: #1d2129;
  line-height: 1.4;
}

.detail-desc {
  border-radius: 4px;
  color: #86909c;
  font-size: 14px;
  line-height: 1.4;
}



.detail-price {
  flex: 1;
  font-size: 20px;
  font-weight: 600;
  color: #f53f3f;
}

.detail-market-price {
  flex: 1;
  font-size: 14px;
  color: #86909c;
  text-decoration: line-through;
}



.detail-footer {
  text-align: right;

  .h-button {
    margin-left: 8px;
  }
}

/* 商品信息卡片样式 */
.product-info-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  // border: 1px solid #e8e8e8;
}

.info-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 400;
  white-space: nowrap;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
}

.price-value {
  font-size: 16px;
  color: #ff4d4f;
  font-weight: 600;
}

.stock-value.in-stock {
  color: #52c41a;
}

.stock-value.out-stock {
  color: #ff4d4f;
}

/* 市场价格行样式 */
.market-price-row {
  margin-bottom: 16px;
  font-size: 14px;
}

.market-price-label {
  color: #86909c;
  margin-right: 8px;
}

.market-price-value {
  color: #86909c;
  text-decoration: line-through;
}

/* 规格选择样式 */
.spec-selection {
  margin: 20px 0;
}

.spec-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.spec-label {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 60px;
  /* 改为最小宽度，支持自适应 */
  text-align: right;
  /* 右对齐，保持整齐 */
  padding-right: 8px;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
  align-items: center;
  align-content: center;
}

.spec-option {
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #1d2129;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
  line-height: 1.2;

  &:hover:not(.spec-option-disabled) {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.spec-option-selected {
  border-color: #1890ff;
  color: #1890ff;
  background: #e6f7ff;
}

.spec-option-disabled {
  border-color: #f5f5f5;
  color: #bfbfbf;
  background: #f5f5f5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .gift-detail {
    flex-direction: column;
  }

  .detail-images {
    flex: none;
    width: 100%;
  }

  .detail-main-image {
    height: 250px;
  }
}
</style>

<style lang="less">
.gift-selection-modal,
.gift-detail-modal {
  .ant-modal-content {  
    background: linear-gradient(181deg, #e4efff 0%, #ffffff 200px, #ffffff 100%) !important;
    border-radius: 16px !important;

    .ant-modal-header {
      background: transparent;
      .ant-modal-title {
        
        font-weight: 500;
        font-size: 24px;
        color: #1d2129;
      }
    }
  }
}
</style>

<!-- 管理端询价 -->

<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  Popconfirm as hPopconfirm,
  message,
  Modal
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { priceInquiryApi } from '@haierbusiness-front/apis';
import {
  IPriceInquiryFilter,
  IPriceInquiry,
  PriceInQuiryStatusConstant,
  getPriceInquiryStatus
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { computed, ref, watch, onMounted, nextTick, h, reactive } from 'vue';
import { usePagination, } from 'vue-request';
import { useEditDialog, useDelete } from '@haierbusiness-front/composables'
import EditDialog from './edit-dialog.vue'
import router from '../../router'
import { SearchOutlined, CopyOutlined } from '@ant-design/icons-vue';
import ColumnFilter from '@haierbusiness-front/components/mice/search/ColumnFilter.vue';
import Actions from '@haierbusiness-front/components/actions/Actions.vue';
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'
import { createVNode } from 'vue';
// 在使用的组件中显式引入
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { text } from 'stream/consumers';
import { log } from 'console';


const currentRouter = ref()


onMounted(async () => {
  currentRouter.value = await router
  listApiRun({
    pageNum: 1,
    pageSize: 10
  })
})

// 添加复制链接的方法
const copyLink = (link: string) => {
  navigator.clipboard.writeText(link).then(() => {
    message.success('链接已复制到剪贴板');
  }).catch(err => {
    console.error('复制失败:', err);
    message.error('复制失败');
  });
};

// 获取完整的询价单链接
const getFullInquiryLink = (record: IPriceInquiry) => {//import.meta.env.VITE_BUSINESS_URL ||
  const baseUrl = import.meta.env.VITE_MICE_BIDMAN_URL;
  return `${baseUrl}#/bidman/priceInquiry/inquiryOrderForm?id=${record.id}&code=${record.code}`;
};

const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: '60px',
    align: 'center',
    customRender: ({ index }) => index + 1,

  },
  {
    title: '酒店名称',
    dataIndex: 'platformHotelName',
    width: '280px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '单据类型',
    dataIndex: 'billType',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      const map: Record<number, string> = {
        1: '引入单',
        2: '变价单'
      };
      const content = map[text as number] || '-';
      return content !== '-' ? h(hTag, { color: text === 1 ? 'blue' : 'green' }, () => content) : content;
    },
  },

  // {
  //   title: '询价时间',
  //   dataIndex: 'inquiryDateStart',
  //   width: '220px',
  //   align: 'center',
  //   customRender: ({ record }) => {
  //     const start = record.inquiryDateStart || '';
  //     const end = record.inquiryDateEnd || '';
  //     return start && end ? `${start} 至 ${end}` : (start || end);
  //   },
  // },
  // {
  //   title: '填报有效期',
  //   dataIndex: 'leadIntoDateStart',
  //   width: '220px',
  //   align: 'center',
  //   customRender: ({ record }) => {
  //     const start = record.leadIntoDateStart || '';
  //     const end = record.leadIntoDateEnd || '';
  //     return start && end ? `${start} 至 ${end}` : (start || end);
  //   },
  // },
  {
    title: '状态',
    dataIndex: 'inquiryState',
    width: '150px',
    align: 'center',
    customRender: ({ text }) => {
      const status = getPriceInquiryStatus(text);
      return status ? status.name : '';
    },

  },
  {
    title: '邀请链接',
    dataIndex: 'inviteLink',
    width: '320px',
    align: 'center',
    ellipsis: true,
    customRender: ({ record,text }) => {
      if (record.billType == 2) {
        console.log('record.billType');
        return  h('div', '-');
      } else if(record.billType == 1) {
        const link = getFullInquiryLink(record);
        return h('div', { style: { display: 'flex', alignItems: 'center', justifyContent: 'space-between' } }, [
          h('span', { style: { flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' } }, link),
          h(hButton, {
            type: 'link',
            icon: h(CopyOutlined),
            onClick: (e: Event) => {
              e.stopPropagation();
              copyLink(link);
            }
          })
        ]);
      }

    }
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
    align: 'center',

  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: '150px',
    align: 'center',
    ellipsis: true,

  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
];

const searchParam = ref<IPriceInquiryFilter>({
  platformHotelName: '',
  inquiryState: undefined,
  createName: '',
  begin: undefined,
  end: undefined,
  billType: undefined,
})
const {
  data,
  run: listApiRun,
  loading,
} = usePagination(priceInquiryApi.list);

const reset = () => {
  searchParam.value = {}
  createTimeRange.value = undefined
  console.log('重置后的搜索参数:', searchParam.value);

  // 使用nextTick确保DOM更新后再调用接口
  nextTick(() => {
    // 重置后立即查询
    const params = {
      pageNum: 1,
      pageSize: 10
    };
    console.log('重置后调用接口参数:', params);
    listApiRun(params);
  });
}

const dataSource = computed(() => data.value?.records || []);


const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total,
  current: data.value?.pageNum,
  pageSize: data.value?.pageSize,
  style: { justifyContent: 'center' },

}));

const handleTableChange = (
  pag: { current: number; pageSize: number },
) => {
  const params = {
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  };
  console.log('最终查询参数:', params);
  listApiRun(params);
};

// 添加跳转到价格询价详情页的方法
const goToDetail = (record: IPriceInquiry, showSubmit: boolean = false) => {
  currentRouter.value.push({
    path: '/bidman/priceInquiry/priceInquiryDetail',
    query: {
      code: record.code, showSubmit: showSubmit ? '1' : '0'
    }
  });
};

// 添加跳转到询价单表单页面的方法
const goToInquiryOrderForm = (record: IPriceInquiry) => {
  currentRouter.value.push({
    path: '/bidman/priceInquiry/inquiryOrderForm',
    query: { id: record.id, code: record.code }
  });
};

// 添加开启权限方法
const enablePermission = async (record: IPriceInquiry) => {
  try {
    console.log('开启权限', record.code);
    await priceInquiryApi.enableSubmitSeason({ inquiryCode: record.code } as IPriceInquiry);
    message.success('开启权限成功');
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  } catch (error) {
    console.error('开启权限失败:', error);
    message.error('开启权限失败');
  }
};

const handlePlaceOpen = async (record: IPriceInquiry) => {
  try {
    console.log('开启会场权限', record);
    await priceInquiryApi.placeOpen({ inquiryCode: record.code } as IPriceInquiry);
    message.success('开启会场权限成功');
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  } catch (error) {
    console.error('开启会场权限失败:', error);
    message.error('开启会场权限失败');
  }
};

// 添加一个刷新列表的方法
const refreshList = () => {
  listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  });
};

// 添加再次提报方法
const submitAgain = (record: IPriceInquiry) => {

  priceInquiryApi.getAssessmentCode({ id: record.id })
    .then(result => {
      if (result && result.code) {
        const processUrl = `https://businessmanagement-test.haier.net/hbweb/process/?code=${result.code}#/details`;
        window.open(processUrl, '_blank');
      } else {
        message.error('获取流程编码失败');
      }
    })
    .catch(error => {
      message.error('获取询价单号失败');
    });
};

// 创建一个空的save方法，满足类型要求
const emptySave = async (request: IPriceInquiry) => {
  console.log('emptySave被调用，但不执行实际操作');
  return { success: true, code: '200', message: 'success' };
};

// eslint-disable-next-line
const { visible, editData, handleCreate, onDialogClose, handleOk } =
  useEditDialog<IPriceInquiry, IPriceInquiry>({
    ...priceInquiryApi,
    save: emptySave
  }, "询价", () => listApiRun({
    ...searchParam.value,
    pageNum: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
  }))

// 处理子组件提交的数据
const handleDialogOk = async (data: any) => {
  try {
    // 在这里调用实际的API
    await priceInquiryApi.hotelCreate(data);
    // 刷新列表
    listApiRun({
      ...searchParam.value,
      pageNum: data.value?.pageNum || 1,
      pageSize: data.value?.pageSize || 10,
    });
  } catch (error) {
    console.error('创建失败:', error);
    message.error('创建失败');
  }
};

// 点击数组中的菜单项
const handleMenuClick = (record: IPriceInquiry, e: MenuInfo) => {
  if (e.key === '1') {
    Modal.confirm({
      title: '确认要开启淡旺季权限吗？',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确定',
      cancelText: '取消',
      onOk() {
        enablePermission(record)
      },
    })
  }
  if (e.key === '3') {

    submitAgain(record)
  }
  if (e.key === '4') {
    goToDetail(record, true)
  }
}
// 更多按钮数组
const computedOptions = (state: number, record: IPriceInquiry) => {
  let options: MenuItemType[] = []
  if (state === 10) {
    // 只有当enableQuarter不为true时才显示"开启淡旺季及阶梯权限"
    if (!record.enableQuarter && record.isLocal) {
      options.push({
        key: '1',
        label: '开启淡旺季权限',
      })
    }
  }
  if (state === 60) {
    options.push({
      key: '3',
      label: '审批查看',
    })
  }
  if (state === 20) {
    options.push({
      key: '4',
      label: '确认',
    })
  }
  return options
}
// 创建时间范围
const createTimeRange = ref<[Dayjs, Dayjs]>()
watch(() => createTimeRange.value, (n: any) => {
  if (n) {
    searchParam.value.createStartTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    searchParam.value.createEndTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    searchParam.value.createStartTime = undefined
    searchParam.value.createEndTime = undefined
  }
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px;">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="platformHotelName">酒店名称：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="platformHotelName" v-model:value="searchParam.platformHotelName" placeholder="请输入酒店名称"
              allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="inquiryState">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select id="inquiryState" v-model:value="searchParam.inquiryState" placeholder="请选择状态" style="width: 100%"
              allow-clear>
              <h-select-option v-for="(value, key) in PriceInQuiryStatusConstant" :key="key" :value="value.type">
                {{ value.name }}
              </h-select-option>
            </h-select>
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="createName">创建人：</label>
          </h-col>
          <h-col :span="4">
            <h-input id="createName" v-model:value="searchParam.createName" placeholder="请输入创建人" allow-clear />
          </h-col>
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="platformHotelName">单据类型</label>
          </h-col>
          <h-col :span="4">
            <h-select id="inquiryState" v-model:value="searchParam.billType" placeholder="请选择单据类型" style="width: 100%"
              allow-clear>
              <h-select-option value=1>引入单</h-select-option>
              <h-select-option value=2>变价单</h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="2" style="text-align: right;padding-right: 10px;">
            <label for="platformHotelName">创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker id="createTime" v-model:value="createTimeRange" value-format="YYYY-MM-DD"
              style="width: 100%" allow-clear />
          </h-col>
        </h-row>

        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="24" style="text-align: right;">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px;">
          <h-col :span="1" style="text-align: left;">
            <h-button type="primary" @click="handleCreate()">
              <PlusOutlined /> 新增
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table :columns="columns" :row-key="record => record.id" :size="'small'" :data-source="dataSource"
          :pagination="pagination" :scroll="{ y: 550 }" :loading="loading" @change="handleTableChange($event as any)">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === '_operator'">
              <h-button type="link" @click="goToDetail(record)">详情</h-button>
              <Actions :menu-options="computedOptions(record.inquiryState, record)"
                :on-menu-click="(key) => handleMenuClick(record, key)"></Actions>
            </template>
            <!-- <template v-else-if="column.dataIndex === 'inviteLink'">
              <div class="link-container">
                <span class="link-text">{{ getFullInquiryLink(record) }}</span>
                <h-button type="link" @click.stop="copyLink(getFullInquiryLink(record))">
                  <CopyOutlined />
                </h-button>
              </div>
            </template> -->
          </template>
        </h-table>
      </h-col>
    </h-row>

    <div v-if="visible">
      <edit-dialog :show="visible" :data="editData" @cancel="onDialogClose" @ok="handleDialogOk">
      </edit-dialog>
    </div>

  </div>
</template>

<style scoped lang="less">
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}

.link-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.link-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.Modify) {
  flex: 0 0 9.333333%;
  max-width: 9.333333%;
}

:where(.css-dev-only-do-not-override-1cqaw7h).ant-col-1 {
  display: block;
  flex: 0 0 5.166667% !important;
  max-width: 5.166667% !important;
}
</style>

<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, RangePicker as hRangePicker, message,
  Upload as hUpload, InputNumber as hInputNumber, Button as hButton, Tag as hTag,
  UploadFile,
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router'
import {
  ITourismProducts, HeaderConstant,
  FileTypeConstant,
  IUserListRequest
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import type { IDomEditor } from '@wangeditor/editor';
import Editor from '@haierbusiness-front/components/editor/Editor.vue';
import { fileApi, tourismProductsApi } from '@haierbusiness-front/apis';
import UserSelect from '@haierbusiness-front/components/user/UserSelect.vue';
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'
import UserSelectPerson from '@haierbusiness-front/components/user/UserSelectPerson.vue';
import dayjs, { Dayjs } from 'dayjs';
import { url } from 'inspector';

// const router = useRouter()
const route = useRoute();
const router = useRouter();

const currentRouter = ref()

const from = ref();
const confirmLoading = ref(false);
const id = ref<number>()
const nickNameList = ref<string[]>([]);

onMounted(async () => {
  currentRouter.value = await router
  const currentId = currentRouter.value.currentRoute.query?.id
  id.value = Number(currentId)
  if (id.value) {
    await get(id.value)
  }
  else {
    tourismProducts.value = {}
  }
})

// watch(() => currentRouter.value?.currentRoute.query, (newValue, oldValue) => {
//   id.value = Number(newValue.id)
//   if (id.value) {
//     get(id.value)
//   }
//   else {
//     tourismProducts.value = {}
//   }
// })

const childRef = ref()

const get = async (id: number) => {
  const data = await tourismProductsApi.get(id)
  if (data && data.id) {
    tourismProducts.value = data
  }
  effectiveTime.value = [
    dayjs(tourismProducts.value.startTime),
    dayjs(tourismProducts.value.endTime)
  ]
  fileList.value = tourismProducts.value?.path ? [{
    id: tourismProducts.value.attachmentId || null, // 提供默认值
    name: tourismProducts.value?.path.split('/').pop()?.split('-').slice(1).join('-'), // 提取文件名
    url: tourismProducts.value.path,
    filePath: tourismProducts.value.path
  }] : []
  imageUrl.value = tourismProducts.value?.path

  nickNameList.value.push(data.nickname)
  const master = [{
    nickName:data.nickname,
    username:data.username
  }]
  childRef.value?.setFirstData(master)
  console.log(nickNameList,"nickNameList");
  
}

const rules = {
  title: [{ required: true, message: '请输入标题' }],
  nickname: [{ required: true, message: '请选择联系人' }],
  startCity: [{ required: true, message: '请输入出发城市' }],
  marketPrice: [{ required: true, message: '请输入市场价格' }],
  staffPrice: [{ required: true, message: '请输入员工价格' }],
};

const tourismProducts = ref<ITourismProducts>({});

//#region 上传相关，没有请删除

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
const baseUrl = import.meta.env.VITE_BUSINESS_URL

const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string | undefined>('');

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！')
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

const upload = async (options: any) => {
  loading.value = true;
  const formData = new FormData()
  formData.append('file', options.file)
  const res = await fileApi.upload(formData)
  const file = {
    ...options.file,
    name: options.file.name,
    url: baseUrl + res.path
  }
  loading.value = false;
  fileList.value = [...fileList.value, file]
  imageUrl.value = baseUrl + res.path
  tourismProducts.value.path = baseUrl + res.path
  tourismProducts.value.type = FileTypeConstant.PICTURE.code

  options.onProgress(100)
  options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
}

// // 移除文件
// const handleRemove = (file: UploadFile) => {
//   const index = fileList.value.indexOf(file);
//   if (index !== -1) {
//     fileList.value.splice(index, 1);
//   }
// };

// 富文本编辑器内容变化处理函数
const onEditorChange = (editor: IDomEditor) => {
  tourismProducts.value.detail = editor.getHtml();
};

// URL验证正则表达式
const urlPattern = /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/;



const handleOk = () => {
  confirmLoading.value = true;
  from.value.validate()
    .then(() => {
      if(!tourismProducts.value.nickname){
        message.info('请选择联系人')
        confirmLoading.value = false;
        return
      }
      if (!tourismProducts.value.externalLinks && !tourismProducts.value.detail) {
        message.info('链接和详情页必须有一个')
        confirmLoading.value = false;
        return
      }
      if(!tourismProducts.value.effectiveTime){
        message.info('请选择生效时间')
        confirmLoading.value = false;
        return
      }
      if(tourismProducts.value.nickname.length > 0 && Array.isArray(tourismProducts.value.nickname)){
        const list = tourismProducts.value.nickname
        tourismProducts.value.nickname = list[0].nickName
        tourismProducts.value.username = list[0].username
      }
      
      const data = {
        ...tourismProducts.value,
      }
      console.log(data, "tourismProducts");

      if (data.id) {
        tourismProductsApi.edit(data).then(res => {
          message.success(`编辑成功!`);
          router.go(-1)
        })
      } else {
        tourismProductsApi.save(data).then(res => {
          message.success(`新增成功!`);
          router.go(-1)
        })
      }
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

const effectiveTime = ref<[Dayjs, Dayjs]>()
watch(() => effectiveTime.value, (n: any, o: any) => {
  tourismProducts.value.effectiveTime = effectiveTime
  if (n) {
    tourismProducts.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
    tourismProducts.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
  } else {
    tourismProducts.value.startTime = undefined
    tourismProducts.value.endTime = undefined
  }
});

const tagList = (list: string | undefined): string[] => {
  if (!list) return []
  return list
    .split('，') // 使用中文逗号分隔
    .map(item => item.trim())
    .filter(item => item.length > 0) // 过滤空字符串
}

// 处理联系人选择变更
const handleManagerChange = (users: any) => {
  console.log('管理员选择变更:', users);

  // 确保users是数组
  const userArray = Array.isArray(users) ? users : [users].filter(Boolean);

  // 创建新的管理员和昵称数组
  const newManagers: any[] = [];
  const newManagerUsernames: string[] = [];

  // 处理每个用户对象
  userArray.forEach(user => {
    // 如果用户是对象格式
    if (typeof user === 'object' && user !== null) {
      // 保留用户对象的所有原始属性
      const manager = { ...user };
      newManagers.push(manager);
      // 使用昵称
      const nickName = manager.nickName || '';
      newManagerUsernames.push(nickName);
    }
  });
  console.log(newManagers,newManagerUsernames,"newManagerUsernames");
  nickNameList.value = newManagerUsernames
  tourismProducts.value.username = newManagers[0].username
};

// 用于UserSelect组件的参数
const userSelectParams = ref<IUserListRequest>({
  pageNum: 1,
  pageSize: 20
});

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-form ref="from" :model="tourismProducts" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" :hide-required-mark="true">
      <h-form-item label="标题" name="title">
        <h-input v-model:value="tourismProducts.title" placeholder="请输入标题" />
      </h-form-item>
      <h-form-item label="产品简介" name="productBrief">
        <h-input v-model:value="tourismProducts.briefIntroduction" placeholder="请输入产品简介" />
      </h-form-item>
      <h-form-item label="联系人" name="nickname">
        <!-- <h-input v-model:value="tourismProducts.nickname" placeholder="请输入联系人" /> -->
        <UserSelectPerson v-model:value="nickNameList" :params="userSelectParams" :multiple="true"
            :max-tag-count="1" :max-count="1" placeholder="请选择产品线管理员" 
            @change="handleManagerChange" ref="childRef" :radio="true"/>
      </h-form-item>
      <h-form-item label="出发城市" name="startCity">
        <h-input v-model:value="tourismProducts.startCity" placeholder="请输入出发城市" />
      </h-form-item>
      <h-form-item label="封面图片上传" name="path">
        <h-upload :file-list="fileList" :before-upload="beforeUpload" :custom-request="upload" :multiple="false"
          :max-count="1" accept=".jpg, .png, .jpeg" list-type="picture-card"
          class="avatar-uploader" :show-upload-list="false">
          <img style="width: 100%;height: 100%;" v-if="imageUrl" :src="imageUrl" />
          <div v-else>
            <loading-outlined v-if="loading"></loading-outlined>
            <plus-outlined v-else></plus-outlined>
            <div class="ant-upload-text">Upload</div>
          </div>
        </h-upload>
        <div class="upload-hint">建议上传图片的尺寸为400*400 ，大小不超过2MB ，格式为png/jpg/jpeg的文件。</div>
      </h-form-item>
      <h-form-item label="市场价格" name="marketPrice">
        <a-input-number v-model:value="tourismProducts.marketPrice" style="width: 100%;">
          <template #addonAfter>
            元
          </template>
        </a-input-number>
      </h-form-item>
      <h-form-item label="员工价格" name="staffPrice">
        <a-input-number v-model:value="tourismProducts.staffPrice" style="width: 100%;">
          <template #addonAfter>
            元
          </template>
        </a-input-number>
      </h-form-item>
      <h-form-item label="外部链接" name="externalLinks">
        <h-input v-model:value="tourismProducts.externalLinks" placeholder="请输入外部链接" show-count />
      </h-form-item>
      <h-form-item label="详情页" name="detail">
        <Editor height="350px" :modelValue="tourismProducts.detail || ''" @change="onEditorChange"
          style="z-index: 20; width: 100%;" uploadUrl="/upload" />
      </h-form-item>
      <h-form-item label="标签" name="tag">
        <h-input v-model:value="tourismProducts.tag" placeholder="请输入标签,可多种，逗号隔开" />
        <template v-if="tourismProducts.tag">
          <a-tag v-for="item in tagList(tourismProducts.tag)" :key="item" color="blue" style="margin:3px">
            {{ item }}
          </a-tag>
        </template>
      </h-form-item>

      <h-form-item label="生效时间" name="effectiveTime">
        <h-range-picker v-model:value="effectiveTime" value-format="YYYY-MM-DD" style="width: 100%" allow-clear />
      </h-form-item>
      <h-form-item style="text-align: center;">
        <h-button type="primary" :loading="confirmLoading" @click="handleOk">{{ tourismProducts.id ? '确认编辑' :
          '确认新增' }}</h-button>
      </h-form-item>
    </h-form>
  </div>

</template>


<style lang="less" scoped>
.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;

}

.upload-hint {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

::deep(.avatar-uploader) {}

:deep(.ant-upload-select) {
  width: 170px !important;
  height: 170px !important;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
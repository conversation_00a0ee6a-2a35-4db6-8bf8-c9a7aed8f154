<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, Textarea as hTextarea
} from 'ant-design-vue';
import { computed, ref, watch } from "vue";
import type { Ref } from "vue";
import {
  IDirectVisaHotel
} from '@haierbusiness-front/common-libs';

interface Props {
  show: boolean;
  data: IDirectVisaHotel | null;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const from = ref();
const confirmLoading = ref(false);

const defaultData: IDirectVisaHotel = {
  name: '',
  code: '',
  state: 0,
  id: null,
  description: ''
};

const directVisaHotel = ref<IDirectVisaHotel>(
  props.data ? props.data : { ...defaultData }
);

watch(() => props.data, (newData) => {
  directVisaHotel.value = newData ? newData : { ...defaultData };
}, { immediate: true });

const emit = defineEmits(["cancel", "ok"]);

const visible = computed(() => props.show);

const handleOk = () => {
  emit("ok", directVisaHotel.value, () => {
    confirmLoading.value = false;
  });
};

const hotalStarList = [
  { text: '白金五星级', value: 55 },
  { text: '五星级', value: 50 },
  { text: '准五星', value: 45 },
  { text: '四星级', value: 40 },
  { text: '准四星', value: 35 },

  { text: '三星级', value: 30 },
  { text: '准三星', value: 25 },
  { text: '二星级', value: 20 },
  { text: '准二星', value: 15 },

  { text: '一星级', value: 10 },
  { text: '准一星', value: 5 },
];

</script>

<template>
  <h-modal v-model:visible="visible" :title="'查看酒店'" width="50%" @cancel="$emit('cancel')"
    :confirmLoading="confirmLoading" @ok="handleOk">
    <div class="top">
      <h-row :align="'middle'" class="how-content">
        <h-col :span="4" style="text-align: right;padding-right: 10px;">
          <label for="createTime">酒店编号：</label>
        </h-col>
        <h-col :span="20">
          {{ directVisaHotel.code }}
        </h-col>
      </h-row>
    </div>
    <div class="top">
      <h-row :align="'middle'" class="how-content">
        <h-col :span="4" style="text-align: right;padding-right: 10px;">
          <label for="createTime">酒店名称：</label>
        </h-col>
        <h-col :span="20">
          {{ directVisaHotel.name }}
        </h-col>
      </h-row>
    </div>
    <div class="top">
      <h-row :align="'middle'" class="how-content">
        <h-col :span="4" style="text-align: right;padding-right: 10px;">
          <label for="createTime">酒店地址：</label>
        </h-col>
        <h-col :span="20">
          {{ directVisaHotel.address }}
        </h-col>
      </h-row>
    </div>
    <div class="top">
      <h-row :align="'middle'" class="how-content">
        <h-col :span="4" style="text-align: right;padding-right: 10px;">
          <label for="createTime">酒店星级：</label>
        </h-col>
        <h-col :span="20">
          <!-- {{ directVisaHotel.starLevel }} -->
          {{hotalStarList.find((item)=> item.value == directVisaHotel.starLevel)?.text || '暂无'}}
        </h-col>
      </h-row>
    </div>
    <div class="top">
      <h-row :align="'middle'" class="how-content">
        <h-col :span="4" style="text-align: right;padding-right: 10px;">
          <label for="createTime">酒店区域：</label>
        </h-col>
        <h-col :span="20">
          {{ directVisaHotel.cityName }}/{{ directVisaHotel.regionName }}
        </h-col>
      </h-row>
    </div>
  </h-modal>
</template>


<style lang="less" scoped>
.important {
  color: red;
}
.how-content{
  padding: 10px;
}
.top{
  margin: 10px 0;
  width: 100%;
}

</style>